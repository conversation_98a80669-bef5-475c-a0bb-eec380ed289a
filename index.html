<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SAIRI AI研究院 - 智能助手</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-light: #3b82f6;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --bg-color: rgba(255, 255, 255, 0.9);
            --user-bubble: rgba(37, 99, 235, 0.1);
            --ai-bubble: rgba(241, 245, 249, 0.8);
            --border-color: rgba(226, 232, 240, 0.5);
            --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.1);
            --rainbow-gradient: linear-gradient(
                45deg,
                #ff0000 0%,
                #ff7f00 15%,
                #ffff00 30%,
                #00ff00 45%,
                #0000ff 60%,
                #4b0082 75%,
                #9400d3 90%,
                #ff0000 100%
            );
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'PingFang SC', 'Microsoft YaHei', sans-serif;
            color: var(--text-primary);
            height: 100vh;
            overflow: hidden;
            position: relative;
            background: linear-gradient(135deg, #e0f2fe 0%, #f0f9ff 100%);
        }

        .water-effect {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="rgba(255,255,255,0.2)" d="M0,192L48,197.3C96,203,192,213,288,229.3C384,245,480,267,576,250.7C672,235,768,181,864,181.3C960,181,1056,235,1152,234.7C1248,235,1344,181,1392,154.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>') bottom center no-repeat;
            background-size: cover;
            opacity: 0.6;
            z-index: -2;
            animation: waterWave 15s ease-in-out infinite alternate;
        }

        @keyframes waterWave {
            0% {
                transform: translateY(0) scale(1);
            }
            50% {
                transform: translateY(-20px) scale(1.02);
            }
            100% {
                transform: translateY(0) scale(1);
            }
        }

        .aurora-effect {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--rainbow-gradient);
            opacity: 0.05;
            z-index: -3;
            animation: auroraFlow 30s linear infinite;
            background-size: 400% 400%;
        }

        @keyframes auroraFlow {
            0% {
                background-position: 0% 50%;
            }
            100% {
                background-position: 100% 50%;
            }
        }

        .app-container {
            width: 100%;
            max-width: 900px;
            height: 95vh;
            margin: 0 auto;
            background-color: var(--bg-color);
            border-radius: 20px;
            box-shadow: var(--shadow-lg);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
            border: 1px solid rgba(255, 255, 255, 0.3);
            transform: scale(0.98);
            opacity: 0;
            animation: appFadeIn 0.6s cubic-bezier(0.22, 1, 0.36, 1) forwards;
        }

        @keyframes appFadeIn {
            to {
                transform: scale(1);
                opacity: 1;
            }
        }

        .header {
            padding: 18px 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background-color: rgba(255, 255, 255, 0.8);
            border-bottom: 1px solid var(--border-color);
            position: relative;
            z-index: 10;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo-icon {
            width: 36px;
            height: 36px;
            border-radius: 10px;
            background: var(--rainbow-gradient);
            background-size: 400% 400%;
            animation: auroraFlow 12s linear infinite;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            box-shadow: var(--shadow-sm);
        }

        .logo-text {
            font-size: 18px;
            font-weight: 600;
            background: var(--rainbow-gradient);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            animation: auroraFlow 18s linear infinite;
            background-size: 400% 400%;
        }

        .style-toggle {
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(226, 232, 240, 0.5);
            border-radius: 20px;
            padding: 6px;
            position: relative;
        }

        .style-toggle::before {
            content: '';
            position: absolute;
            top: 6px;
            left: 6px;
            width: 50%;
            height: calc(100% - 12px);
            background: white;
            border-radius: 16px;
            box-shadow: var(--shadow-sm);
            transition: transform 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            z-index: 1;
        }

        .style-toggle.concise::before {
            transform: translateX(0);
        }

        .style-toggle.detailed::before {
            transform: translateX(100%);
        }

        .style-option {
            padding: 6px 12px;
            font-size: 13px;
            font-weight: 500;
            color: var(--text-secondary);
            cursor: pointer;
            position: relative;
            z-index: 2;
            transition: color 0.3s ease;
        }

        .style-toggle.concise .style-option:nth-child(1),
        .style-toggle.detailed .style-option:nth-child(2) {
            color: var(--primary-color);
        }

        .chat-container {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .message {
            max-width: 80%;
            margin-bottom: 16px;
            position: relative;
            opacity: 0;
            transform: translateY(20px);
            animation: messageIn 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        @keyframes messageIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            object-fit: cover;
            flex-shrink: 0;
            box-shadow: var(--shadow-sm);
            border: 2px solid white;
        }

        .message-content {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .user-message {
            align-self: flex-end;
        }

        .user-message .message-bubble {
            background-color: var(--user-bubble);
            color: var(--text-primary);
            border-radius: 18px 18px 0 18px;
            border: 1px solid rgba(37, 99, 235, 0.2);
        }

        .ai-message {
            align-self: flex-start;
        }

        .ai-message .message-bubble {
            background-color: var(--ai-bubble);
            color: var(--text-primary);
            border-radius: 18px 18px 18px 0;
            border: 1px solid rgba(226, 232, 240, 0.5);
        }

        .message-bubble {
            padding: 12px 16px;
            font-size: 15px;
            line-height: 1.5;
            box-shadow: var(--shadow-sm);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            position: relative;
            overflow: hidden;
        }

        .message-bubble::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: var(--rainbow-gradient);
            background-size: 400% 400%;
            opacity: 0;
            z-index: -1;
            border-radius: 20px;
            transition: opacity 0.3s ease;
        }

        .message:hover .message-bubble::after {
            opacity: 0.3;
        }

        .think-message .message-bubble {
            font-size: 13px;
            color: var(--text-secondary);
            padding: 10px 14px;
            position: relative;
            overflow: visible;
        }

        .think-message .message-bubble::after {
            opacity: 0.5;
            animation: auroraFlow 6s linear infinite;
        }

        .think-message.fade-out {
            animation: thinkFadeOut 0.6s cubic-bezier(0.55, 0.085, 0.68, 0.53) forwards;
        }

        @keyframes thinkFadeOut {
            to {
                opacity: 0;
                transform: translateY(-20px) scale(0.9);
                height: 0;
                padding: 0;
                margin: 0;
            }
        }

        .message-time {
            font-size: 11px;
            color: var(--text-secondary);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .message:hover .message-time {
            opacity: 1;
        }

        .input-container {
            padding: 16px 20px;
            background-color: rgba(255, 255, 255, 0.8);
            border-top: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            gap: 12px;
            position: relative;
        }

        .input-box {
            flex: 1;
            border: 1px solid var(--border-color);
            border-radius: 20px;
            padding: 14px 20px;
            font-size: 15px;
            background-color: rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
            outline: none;
            font-family: inherit;
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            box-shadow: var(--shadow-sm);
        }

        .input-box:focus {
            border-color: var(--primary-light);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
        }

        .send-btn {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: var(--rainbow-gradient);
            background-size: 400% 400%;
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
            animation: auroraFlow 12s linear infinite;
        }

        .send-btn:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: var(--shadow-lg);
        }

        .send-btn:active {
            transform: translateY(0) scale(0.98);
        }

        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
            align-self: flex-start;
            opacity: 0;
            transform: scale(0.8);
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        .typing-indicator.active {
            opacity: 1;
            transform: scale(1);
        }

        .typing-dots {
            display: flex;
            gap: 6px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background-color: var(--text-secondary);
            border-radius: 50%;
            animation: typingBounce 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) {
            animation-delay: 0s;
        }

        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typingBounce {
            0%, 60%, 100% {
                transform: translateY(0);
            }
            30% {
                transform: translateY(-6px);
            }
        }

        .welcome-screen {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 40px;
            background-color: rgba(255, 255, 255, 0.9);
            z-index: 5;
            transition: all 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
        }

        .welcome-screen.hidden {
            opacity: 0;
            pointer-events: none;
            transform: translateY(30px);
        }

        .welcome-logo {
            width: 80px;
            height: 80px;
            border-radius: 22px;
            background: var(--rainbow-gradient);
            background-size: 400% 400%;
            animation: auroraFlow 12s linear infinite;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 24px;
            box-shadow: var(--shadow-md);
        }

        .welcome-logo-icon {
            width: 60%;
            height: 60%;
            object-fit: contain;
        }

        .welcome-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 12px;
            background: var(--rainbow-gradient);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            animation: auroraFlow 18s linear infinite;
            background-size: 400% 400%;
            text-align: center;
        }

        .welcome-subtitle {
            font-size: 16px;
            color: var(--text-secondary);
            margin-bottom: 32px;
            text-align: center;
            max-width: 600px;
            line-height: 1.6;
        }

        .welcome-input {
            width: 100%;
            max-width: 600px;
            border: 1px solid var(--border-color);
            border-radius: 20px;
            padding: 16px 24px;
            font-size: 16px;
            background-color: rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
            outline: none;
            font-family: inherit;
            box-shadow: var(--shadow-sm);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
        }

        .welcome-input:focus {
            border-color: var(--primary-light);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
        }

        .suggestion-chips {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 12px;
            margin-top: 24px;
            max-width: 600px;
        }

        .suggestion-chip {
            padding: 10px 18px;
            background-color: rgba(241, 245, 249, 0.8);
            border-radius: 18px;
            font-size: 14px;
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            box-shadow: var(--shadow-sm);
        }

        .suggestion-chip:hover {
            background-color: white;
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .suggestion-chip::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border-radius: 20px;
            background: var(--rainbow-gradient);
            background-size: 400% 400%;
            opacity: 0;
            z-index: -1;
            transition: opacity 0.3s ease;
        }

        .suggestion-chip:hover::after {
            opacity: 0.3;
        }

        .mode-indicator {
            position: absolute;
            top: -10px;
            right: 20px;
            background: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            color: var(--primary-color);
            box-shadow: var(--shadow-sm);
            transform: translateY(10px);
            opacity: 0;
            transition: all 0.3s ease;
        }

        .mode-indicator.show {
            transform: translateY(0);
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="water-effect"></div>
    <div class="aurora-effect"></div>

    <div class="app-container">
        <div class="header">
            <div class="logo">
                <div class="logo-icon">SAIRI</div>
                <div class="logo-text">AI研究院</div>
            </div>
            <div class="style-toggle concise" id="styleToggle">
                <div class="style-option" data-style="concise">简洁明快</div>
                <div class="style-option" data-style="detailed">详细耐心</div>
                <div class="mode-indicator" id="modeIndicator">简洁模式</div>
            </div>
        </div>

        <div class="chat-container" id="chatContainer"></div>

        <div class="input-container" id="inputContainer">
            <input type="text" class="input-box" id="messageInput" placeholder="输入消息...">
            <button class="send-btn" id="sendBtn">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="22" y1="2" x2="11" y2="13"></line>
                    <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                </svg>
            </button>
        </div>

        <div class="welcome-screen" id="welcomeScreen">
            <div class="welcome-logo">
                <svg class="welcome-logo-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"></path>
                </svg>
            </div>
            <h1 class="welcome-title">SAIRI AI研究院智能助手</h1>
            <p class="welcome-subtitle">基于Qwen3:8b模型的先进人工智能系统，为您提供专业、智能的交互体验</p>
            <input type="text" class="welcome-input" id="welcomeInput" placeholder="输入您的问题或指令...">
            <div class="suggestion-chips">
                <div class="suggestion-chip">解释深度学习原理</div>
                <div class="suggestion-chip">生成Python数据分析代码</div>
                <div class="suggestion-chip">撰写研究论文摘要</div>
                <div class="suggestion-chip">讨论AI伦理问题</div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const chatContainer = document.getElementById('chatContainer');
            const welcomeScreen = document.getElementById('welcomeScreen');
            const welcomeInput = document.getElementById('welcomeInput');
            const inputContainer = document.getElementById('inputContainer');
            const messageInput = document.getElementById('messageInput');
            const sendBtn = document.getElementById('sendBtn');
            const suggestionChips = document.querySelectorAll('.suggestion-chip');
            const styleToggle = document.getElementById('styleToggle');
            const styleOptions = document.querySelectorAll('.style-option');
            const modeIndicator = document.getElementById('modeIndicator');
            
            let isThinking = false;
            let currentThinkElement = null;
            let currentResponseElement = null;
            let typingIndicator = null;
            let currentStyle = 'concise';
            let responseMode = 'concise';

            // 初始化欢迎界面输入
            welcomeInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && welcomeInput.value.trim()) {
                    startChat(welcomeInput.value.trim());
                }
            });

            // 初始化聊天输入
            messageInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && messageInput.value.trim()) {
                    sendMessage(messageInput.value.trim());
                }
            });

            sendBtn.addEventListener('click', () => {
                if (messageInput.value.trim()) {
                    sendMessage(messageInput.value.trim());
                }
            });

            // 建议芯片点击事件
            suggestionChips.forEach(chip => {
                chip.addEventListener('click', () => {
                    startChat(chip.textContent);
                });
            });

            // 样式切换
            styleOptions.forEach(option => {
                option.addEventListener('click', () => {
                    const style = option.dataset.style;
                    if (style !== currentStyle) {
                        currentStyle = style;
                        responseMode = style;
                        styleToggle.classList.toggle('concise');
                        styleToggle.classList.toggle('detailed');
                        
                        // 显示模式提示
                        modeIndicator.textContent = style === 'concise' ? '简洁模式' : '详细模式';
                        modeIndicator.classList.add('show');
                        
                        setTimeout(() => {
                            modeIndicator.classList.remove('show');
                        }, 2000);
                    }
                });
            });

            function startChat(initialMessage) {
                // 隐藏欢迎界面
                welcomeScreen.classList.add('hidden');
                inputContainer.style.display = 'flex';
                
                // 设置输入框焦点
                setTimeout(() => {
                    messageInput.focus();
                    sendMessage(initialMessage);
                }, 600);
            }

            function sendMessage(message) {
                // 清除输入
                messageInput.value = '';
                
                // 添加用户消息
                addMessage(message, 'user');

                // 显示AI正在输入的指示器
                typingIndicator = showTypingIndicator();

                // 发送请求
                fetchAIResponse(message);
            }

            function addMessage(text, sender, isThink = false) {
                const messageDiv = document.createElement('div');
                messageDiv.classList.add('message');
                
                if (sender === 'user') {
                    messageDiv.classList.add('user-message');
                } else {
                    messageDiv.classList.add('ai-message');
                }

                // 创建头像
                const avatar = document.createElement('img');
                avatar.classList.add('avatar');
                avatar.src = sender === 'user' ? '1.jpg' : '2.jpg';
                avatar.alt = sender === 'user' ? '用户头像' : 'AI头像';
                
                // 创建消息内容
                const contentDiv = document.createElement('div');
                contentDiv.classList.add('message-content');
                
                const bubbleDiv = document.createElement('div');
                bubbleDiv.classList.add('message-bubble');
                if (isThink) {
                    bubbleDiv.classList.add('think-bubble');
                }
                bubbleDiv.textContent = text;
                
                const timeDiv = document.createElement('div');
                timeDiv.classList.add('message-time');
                timeDiv.textContent = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                
                contentDiv.appendChild(bubbleDiv);
                contentDiv.appendChild(timeDiv);
                
                messageDiv.appendChild(avatar);
                messageDiv.appendChild(contentDiv);
                
                if (isThink) {
                    messageDiv.classList.add('think-message');
                }
                
                chatContainer.appendChild(messageDiv);
                
                // 滚动到底部
                chatContainer.scrollTop = chatContainer.scrollHeight;
                
                return messageDiv;
            }

            function showTypingIndicator() {
                const typingDiv = document.createElement('div');
                typingDiv.classList.add('typing-indicator');
                
                const avatar = document.createElement('img');
                avatar.classList.add('avatar');
                avatar.src = '2.jpg';
                avatar.alt = 'AI头像';
                
                const dotsDiv = document.createElement('div');
                dotsDiv.classList.add('typing-dots');
                dotsDiv.innerHTML = `
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                `;
                
                typingDiv.appendChild(avatar);
                typingDiv.appendChild(dotsDiv);
                
                chatContainer.appendChild(typingDiv);
                
                // 强制重排以触发动画
                void typingDiv.offsetWidth;
                
                typingDiv.classList.add('active');
                
                return typingDiv;
            }

            function hideTypingIndicator() {
                if (typingIndicator) {
                    typingIndicator.classList.remove('active');
                    setTimeout(() => {
                        typingIndicator.remove();
                        typingIndicator = null;
                    }, 300);
                }
            }

            async function fetchAIResponse(prompt) {
                let buffer = '';
                let inThinkTag = false;
                let thinkContent = '';
                
                // 根据模式调整提示词
                const fullPrompt = responseMode === 'detailed' 
                    ? `${prompt}\n\n请提供详细、全面的回答，包含相关背景知识和具体示例。`
                    : `${prompt}\n\n请提供简洁明了的回答，直接回答问题核心。`;
                
                try {
                    const response = await fetch('http://localhost:11434/api/generate', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            model: 'qwen3:8b',
                            prompt: fullPrompt,
                            stream: true
                        })
                    });
                    
                    if (!response.ok) throw new Error('网络响应不正常');
                    
                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();
                    
                    while (true) {
                        const { done, value } = await reader.read();
                        if (done) break;
                        
                        const chunk = decoder.decode(value);
                        const lines = chunk.split('\n').filter(line => line.trim() !== '');
                        
                        for (const line of lines) {
                            try {
                                const data = JSON.parse(line);
                                if (data.response) {
                                    buffer += data.response;
                                    
                                    // 处理思考标签
                                    if (buffer.includes('<think>') || inThinkTag) {
                                        if (!inThinkTag) {
                                            // 开始思考
                                            const thinkStart = buffer.indexOf('<think>');
                                            const beforeThink = buffer.substring(0, thinkStart);
                                            
                                            if (beforeThink) {
                                                // 如果有思考标签前的内容，先显示
                                                if (!currentResponseElement) {
                                                    currentResponseElement = addMessage(beforeThink, 'ai');
                                                } else {
                                                    currentResponseElement.querySelector('.message-bubble').textContent += beforeThink;
                                                }
                                            }
                                            
                                            buffer = buffer.substring(thinkStart + '<think>'.length);
                                            inThinkTag = true;
                                            currentThinkElement = addMessage('', 'ai', true);
                                        }
                                        
                                        // 检查思考结束
                                        if (buffer.includes('</think>')) {
                                            const thinkEnd = buffer.indexOf('</think>');
                                            thinkContent += buffer.substring(0, thinkEnd);
                                            currentThinkElement.querySelector('.message-bubble').textContent = thinkContent;
                                            
                                            // 添加消失动画
                                            setTimeout(() => {
                                                currentThinkElement.classList.add('fade-out');
                                                setTimeout(() => {
                                                    currentThinkElement.remove();
                                                    currentThinkElement = null;
                                                }, 600);
                                            }, 800);
                                            
                                            buffer = buffer.substring(thinkEnd + '</think>'.length);
                                            inThinkTag = false;
                                            thinkContent = '';
                                        } else {
                                            thinkContent += buffer;
                                            currentThinkElement.querySelector('.message-bubble').textContent = thinkContent;
                                            buffer = '';
                                        }
                                    } else {
                                        // 普通响应
                                        if (!currentResponseElement) {
                                            currentResponseElement = addMessage(buffer, 'ai');
                                        } else {
                                            currentResponseElement.querySelector('.message-bubble').textContent += data.response;
                                        }
                                        buffer = '';
                                    }
                                    
                                    chatContainer.scrollTop = chatContainer.scrollHeight;
                                }
                            } catch (e) {
                                console.error('解析错误:', e);
                            }
                        }
                    }
                    
                    // 处理剩余内容
                    if (buffer) {
                        if (!currentResponseElement) {
                            currentResponseElement = addMessage(buffer, 'ai');
                        } else {
                            currentResponseElement.querySelector('.message-bubble').textContent += buffer;
                        }
                    }
                    
                } catch (error) {
                    addMessage(`抱歉，发生了错误: ${error.message}`, 'ai');
                } finally {
                    hideTypingIndicator();
                    currentResponseElement = null;
                    currentThinkElement = null;
                }
            }
        });
    </script>
</body>
</html>